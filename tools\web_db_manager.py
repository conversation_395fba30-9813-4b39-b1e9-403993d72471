#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化数据库管理工具
基于FastAPI和HTML的Web界面数据库管理工具
"""

import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from fastapi import FastAPI, HTTPException, Request, Form
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn

from database_manager import DatabaseManager


class WebDatabaseManager:
    """Web数据库管理器"""
    
    def __init__(self):
        self.app = FastAPI(
            title="Yuan数据库管理工具",
            description="可视化数据库管理界面",
            version="1.0.0"
        )
        self.db_manager = DatabaseManager()
        self.templates = Jinja2Templates(directory="templates")
        
        # 创建静态文件目录
        import os
        os.makedirs("static", exist_ok=True)
        os.makedirs("templates", exist_ok=True)
        
        self.setup_routes()
        self.create_templates()
        self.create_static_files()
    
    def setup_routes(self):
        """设置路由"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard(request: Request):
            """主页面"""
            return self.templates.TemplateResponse(
                "dashboard.html", 
                {"request": request, "title": "数据库管理"}
            )
        
        @self.app.get("/api/databases")
        async def get_databases():
            """获取数据库列表"""
            try:
                databases = []
                for service, db_path in self.db_manager.databases.items():
                    try:
                        info = self.db_manager.get_database_info(service)
                        databases.append({
                            "service": service,
                            "path": info["path"],
                            "size": info["size"],
                            "size_formatted": info["size_formatted"],
                            "modified": info["modified"],
                            "table_count": len(info["tables"]),
                            "total_records": sum(t["count"] for t in info["tables"].values())
                        })
                    except Exception as e:
                        databases.append({
                            "service": service,
                            "path": db_path,
                            "error": str(e)
                        })
                
                return {"success": True, "data": databases}
            except Exception as e:
                return {"success": False, "error": str(e)}
        
        @self.app.get("/api/database/{service}")
        async def get_database_info(service: str):
            """获取数据库详细信息"""
            try:
                info = self.db_manager.get_database_info(service)
                return {"success": True, "data": info}
            except Exception as e:
                return {"success": False, "error": str(e)}
        
        @self.app.get("/api/database/{service}/table/{table}")
        async def get_table_data(
            service: str, 
            table: str, 
            limit: int = 50, 
            offset: int = 0,
            where: Optional[str] = None
        ):
            """获取表数据"""
            try:
                # 需要修改database_manager的query_table方法来支持offset
                # 暂时使用简单的方法
                all_rows = self.db_manager.query_table(service, table, None, where)
                total = len(all_rows) if all_rows else 0

                # 手动分页
                start_idx = offset
                end_idx = offset + limit
                rows = all_rows[start_idx:end_idx] if all_rows else []
                
                # total已经在上面计算了
                
                return {
                    "success": True,
                    "data": {
                        "rows": rows,
                        "total": total,
                        "limit": limit,
                        "offset": offset
                    }
                }
            except Exception as e:
                return {"success": False, "error": str(e)}
        
        @self.app.post("/api/database/{service}/backup")
        async def backup_database(service: str):
            """备份数据库"""
            try:
                backup_file = self.db_manager.backup_database(service)
                return {
                    "success": True, 
                    "message": f"备份完成: {backup_file}",
                    "backup_file": backup_file
                }
            except Exception as e:
                return {"success": False, "error": str(e)}
        
        @self.app.post("/api/database/{service}/vacuum")
        async def vacuum_database(service: str):
            """清理数据库"""
            try:
                self.db_manager.vacuum_database(service)
                return {"success": True, "message": "数据库清理完成"}
            except Exception as e:
                return {"success": False, "error": str(e)}
        
        @self.app.get("/database/{service}", response_class=HTMLResponse)
        async def database_detail(request: Request, service: str):
            """数据库详情页面"""
            return self.templates.TemplateResponse(
                "database_detail.html",
                {"request": request, "service": service, "title": f"数据库: {service}"}
            )
        
        @self.app.get("/database/{service}/table/{table}", response_class=HTMLResponse)
        async def table_detail(request: Request, service: str, table: str):
            """表详情页面"""
            return self.templates.TemplateResponse(
                "table_detail.html",
                {
                    "request": request, 
                    "service": service, 
                    "table": table,
                    "title": f"表: {service}.{table}"
                }
            )
    
    def create_templates(self):
        """创建HTML模板"""
        
        # 基础模板
        base_template = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Yuan数据库管理{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar { min-height: 100vh; background-color: #f8f9fa; }
        .table-container { max-height: 600px; overflow-y: auto; }
        .loading { display: none; }
        .error { color: #dc3545; }
        .success { color: #198754; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <h5><i class="bi bi-database"></i> Yuan数据库</h5>
                    <ul class="nav flex-column" id="database-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/"><i class="bi bi-house"></i> 首页</a>
                        </li>
                    </ul>
                </div>
            </nav>
            <main class="col-md-10 ms-sm-auto px-md-4">
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 加载数据库列表到侧边栏
        async function loadDatabaseNav() {
            try {
                const response = await fetch('/api/databases');
                const result = await response.json();
                if (result.success) {
                    const nav = document.getElementById('database-nav');
                    result.data.forEach(db => {
                        const li = document.createElement('li');
                        li.className = 'nav-item';
                        li.innerHTML = `<a class="nav-link" href="/database/${db.service}">
                            <i class="bi bi-server"></i> ${db.service}
                        </a>`;
                        nav.appendChild(li);
                    });
                }
            } catch (error) {
                console.error('加载数据库列表失败:', error);
            }
        }
        
        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', loadDatabaseNav);
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>'''
        
        with open("templates/base.html", "w", encoding="utf-8") as f:
            f.write(base_template)
        
        # 主页模板
        dashboard_template = '''{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-speedometer2"></i> 数据库管理面板</h1>
    <button class="btn btn-primary" onclick="refreshDatabases()">
        <i class="bi bi-arrow-clockwise"></i> 刷新
    </button>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-list"></i> 数据库列表</h5>
            </div>
            <div class="card-body">
                <div id="loading" class="loading text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
                <div id="database-list"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
async function loadDatabases() {
    const loading = document.getElementById('loading');
    const list = document.getElementById('database-list');
    
    loading.style.display = 'block';
    list.innerHTML = '';
    
    try {
        const response = await fetch('/api/databases');
        const result = await response.json();
        
        if (result.success) {
            if (result.data.length === 0) {
                list.innerHTML = '<p class="text-muted">未发现任何数据库</p>';
            } else {
                const table = document.createElement('table');
                table.className = 'table table-striped';
                table.innerHTML = `
                    <thead>
                        <tr>
                            <th>服务名称</th>
                            <th>文件大小</th>
                            <th>表数量</th>
                            <th>总记录数</th>
                            <th>修改时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${result.data.map(db => `
                            <tr>
                                <td><a href="/database/${db.service}">${db.service}</a></td>
                                <td>${db.size_formatted || 'N/A'}</td>
                                <td>${db.table_count || 'N/A'}</td>
                                <td>${db.total_records || 'N/A'}</td>
                                <td>${db.modified ? new Date(db.modified).toLocaleString() : 'N/A'}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="backupDatabase('${db.service}')">
                                        <i class="bi bi-download"></i> 备份
                                    </button>
                                    <button class="btn btn-sm btn-outline-warning" onclick="vacuumDatabase('${db.service}')">
                                        <i class="bi bi-gear"></i> 清理
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                `;
                list.appendChild(table);
            }
        } else {
            list.innerHTML = `<p class="error">加载失败: ${result.error}</p>`;
        }
    } catch (error) {
        list.innerHTML = `<p class="error">网络错误: ${error.message}</p>`;
    } finally {
        loading.style.display = 'none';
    }
}

async function backupDatabase(service) {
    if (!confirm(`确认备份数据库 ${service}?`)) return;
    
    try {
        const response = await fetch(`/api/database/${service}/backup`, {method: 'POST'});
        const result = await response.json();
        
        if (result.success) {
            alert(`备份成功: ${result.backup_file}`);
        } else {
            alert(`备份失败: ${result.error}`);
        }
    } catch (error) {
        alert(`备份失败: ${error.message}`);
    }
}

async function vacuumDatabase(service) {
    if (!confirm(`确认清理数据库 ${service}? 此操作会锁定数据库。`)) return;
    
    try {
        const response = await fetch(`/api/database/${service}/vacuum`, {method: 'POST'});
        const result = await response.json();
        
        if (result.success) {
            alert('数据库清理完成');
            refreshDatabases();
        } else {
            alert(`清理失败: ${result.error}`);
        }
    } catch (error) {
        alert(`清理失败: ${error.message}`);
    }
}

function refreshDatabases() {
    loadDatabases();
}

// 页面加载时执行
document.addEventListener('DOMContentLoaded', loadDatabases);
</script>
{% endblock %}'''
        
        with open("templates/dashboard.html", "w", encoding="utf-8") as f:
            f.write(dashboard_template)

        # 数据库详情模板
        database_detail_template = '''{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-server"></i> 数据库: {{ service }}</h1>
    <div>
        <button class="btn btn-outline-primary" onclick="backupDatabase()">
            <i class="bi bi-download"></i> 备份
        </button>
        <button class="btn btn-outline-warning" onclick="vacuumDatabase()">
            <i class="bi bi-gear"></i> 清理
        </button>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-info-circle"></i> 数据库信息</h5>
            </div>
            <div class="card-body" id="db-info">
                <div class="loading text-center">
                    <div class="spinner-border" role="status"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-table"></i> 数据表</h5>
            </div>
            <div class="card-body" id="tables-list">
                <div class="loading text-center">
                    <div class="spinner-border" role="status"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
const SERVICE = '{{ service }}';

async function loadDatabaseInfo() {
    try {
        const response = await fetch(`/api/database/${SERVICE}`);
        const result = await response.json();

        const infoDiv = document.getElementById('db-info');
        const tablesDiv = document.getElementById('tables-list');

        if (result.success) {
            const info = result.data;

            // 显示数据库信息
            infoDiv.innerHTML = `
                <p><strong>路径:</strong><br><small>${info.path}</small></p>
                <p><strong>大小:</strong> ${info.size_formatted}</p>
                <p><strong>修改时间:</strong><br><small>${new Date(info.modified).toLocaleString()}</small></p>
                <p><strong>表数量:</strong> ${Object.keys(info.tables).length}</p>
            `;

            // 显示表列表
            if (Object.keys(info.tables).length === 0) {
                tablesDiv.innerHTML = '<p class="text-muted">无数据表</p>';
            } else {
                const table = document.createElement('table');
                table.className = 'table table-hover';
                table.innerHTML = `
                    <thead>
                        <tr>
                            <th>表名</th>
                            <th>记录数</th>
                            <th>列数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${Object.entries(info.tables).map(([tableName, tableInfo]) => `
                            <tr>
                                <td><a href="/database/${SERVICE}/table/${tableName}">${tableName}</a></td>
                                <td>${tableInfo.count}</td>
                                <td>${tableInfo.columns.length}</td>
                                <td>
                                    <a href="/database/${SERVICE}/table/${tableName}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i> 查看
                                    </a>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                `;
                tablesDiv.appendChild(table);
            }
        } else {
            infoDiv.innerHTML = `<p class="error">加载失败: ${result.error}</p>`;
            tablesDiv.innerHTML = '';
        }
    } catch (error) {
        document.getElementById('db-info').innerHTML = `<p class="error">网络错误: ${error.message}</p>`;
    }
}

async function backupDatabase() {
    if (!confirm(`确认备份数据库 ${SERVICE}?`)) return;

    try {
        const response = await fetch(`/api/database/${SERVICE}/backup`, {method: 'POST'});
        const result = await response.json();

        if (result.success) {
            alert(`备份成功: ${result.backup_file}`);
        } else {
            alert(`备份失败: ${result.error}`);
        }
    } catch (error) {
        alert(`备份失败: ${error.message}`);
    }
}

async function vacuumDatabase() {
    if (!confirm(`确认清理数据库 ${SERVICE}? 此操作会锁定数据库。`)) return;

    try {
        const response = await fetch(`/api/database/${SERVICE}/vacuum`, {method: 'POST'});
        const result = await response.json();

        if (result.success) {
            alert('数据库清理完成');
            loadDatabaseInfo();
        } else {
            alert(`清理失败: ${result.error}`);
        }
    } catch (error) {
        alert(`清理失败: ${error.message}`);
    }
}

document.addEventListener('DOMContentLoaded', loadDatabaseInfo);
</script>
{% endblock %}'''

        with open("templates/database_detail.html", "w", encoding="utf-8") as f:
            f.write(database_detail_template)

        # 表详情模板
        table_detail_template = '''{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-table"></i> 表: {{ service }}.{{ table }}</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">首页</a></li>
            <li class="breadcrumb-item"><a href="/database/{{ service }}">{{ service }}</a></li>
            <li class="breadcrumb-item active">{{ table }}</li>
        </ol>
    </nav>
</div>

<div class="row mb-3">
    <div class="col-md-6">
        <div class="input-group">
            <input type="text" class="form-control" id="where-condition" placeholder="WHERE条件 (例: id > 10)">
            <button class="btn btn-outline-secondary" onclick="applyFilter()">
                <i class="bi bi-funnel"></i> 过滤
            </button>
            <button class="btn btn-outline-secondary" onclick="clearFilter()">
                <i class="bi bi-x"></i> 清除
            </button>
        </div>
    </div>
    <div class="col-md-6 text-end">
        <select class="form-select d-inline-block w-auto" id="page-size" onchange="changePageSize()">
            <option value="10">10条/页</option>
            <option value="25" selected>25条/页</option>
            <option value="50">50条/页</option>
            <option value="100">100条/页</option>
        </select>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5><i class="bi bi-list-ul"></i> 表数据</h5>
        <span id="record-info" class="text-muted"></span>
    </div>
    <div class="card-body">
        <div id="loading" class="loading text-center">
            <div class="spinner-border" role="status"></div>
        </div>
        <div class="table-container">
            <div id="table-data"></div>
        </div>
        <nav id="pagination" class="mt-3"></nav>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
const SERVICE = '{{ service }}';
const TABLE = '{{ table }}';
let currentPage = 0;
let pageSize = 25;
let whereCondition = '';
let totalRecords = 0;

async function loadTableData(page = 0, size = 25, where = '') {
    const loading = document.getElementById('loading');
    const tableDiv = document.getElementById('table-data');
    const recordInfo = document.getElementById('record-info');

    loading.style.display = 'block';
    tableDiv.innerHTML = '';

    try {
        const offset = page * size;
        let url = `/api/database/${SERVICE}/table/${TABLE}?limit=${size}&offset=${offset}`;
        if (where) {
            url += `&where=${encodeURIComponent(where)}`;
        }

        const response = await fetch(url);
        const result = await response.json();

        if (result.success) {
            const data = result.data;
            totalRecords = data.total;

            // 更新记录信息
            const start = offset + 1;
            const end = Math.min(offset + size, totalRecords);
            recordInfo.textContent = `显示 ${start}-${end} / 共 ${totalRecords} 条记录`;

            if (data.rows.length === 0) {
                tableDiv.innerHTML = '<p class="text-muted">无数据</p>';
            } else {
                // 创建表格
                const table = document.createElement('table');
                table.className = 'table table-striped table-hover';

                // 表头
                const columns = Object.keys(data.rows[0]);
                const thead = document.createElement('thead');
                thead.innerHTML = `<tr>${columns.map(col => `<th>${col}</th>`).join('')}</tr>`;
                table.appendChild(thead);

                // 表体
                const tbody = document.createElement('tbody');
                data.rows.forEach(row => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = columns.map(col => {
                        let value = row[col];
                        if (value === null) value = '<span class="text-muted">NULL</span>';
                        else if (typeof value === 'string' && value.length > 50) {
                            value = value.substring(0, 50) + '...';
                        }
                        return `<td>${value}</td>`;
                    }).join('');
                    tbody.appendChild(tr);
                });
                table.appendChild(tbody);

                tableDiv.appendChild(table);
            }

            // 更新分页
            updatePagination(page, size, totalRecords);

        } else {
            tableDiv.innerHTML = `<p class="error">加载失败: ${result.error}</p>`;
        }
    } catch (error) {
        tableDiv.innerHTML = `<p class="error">网络错误: ${error.message}</p>`;
    } finally {
        loading.style.display = 'none';
    }
}

function updatePagination(page, size, total) {
    const pagination = document.getElementById('pagination');
    const totalPages = Math.ceil(total / size);

    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let html = '<ul class="pagination justify-content-center">';

    // 上一页
    if (page > 0) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${page - 1})">上一页</a></li>`;
    } else {
        html += '<li class="page-item disabled"><span class="page-link">上一页</span></li>';
    }

    // 页码
    const startPage = Math.max(0, page - 2);
    const endPage = Math.min(totalPages - 1, page + 2);

    if (startPage > 0) {
        html += '<li class="page-item"><a class="page-link" href="#" onclick="goToPage(0)">1</a></li>';
        if (startPage > 1) html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
    }

    for (let i = startPage; i <= endPage; i++) {
        if (i === page) {
            html += `<li class="page-item active"><span class="page-link">${i + 1}</span></li>`;
        } else {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${i})">${i + 1}</a></li>`;
        }
    }

    if (endPage < totalPages - 1) {
        if (endPage < totalPages - 2) html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${totalPages - 1})">${totalPages}</a></li>`;
    }

    // 下一页
    if (page < totalPages - 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${page + 1})">下一页</a></li>`;
    } else {
        html += '<li class="page-item disabled"><span class="page-link">下一页</span></li>';
    }

    html += '</ul>';
    pagination.innerHTML = html;
}

function goToPage(page) {
    currentPage = page;
    loadTableData(currentPage, pageSize, whereCondition);
}

function changePageSize() {
    pageSize = parseInt(document.getElementById('page-size').value);
    currentPage = 0;
    loadTableData(currentPage, pageSize, whereCondition);
}

function applyFilter() {
    whereCondition = document.getElementById('where-condition').value.trim();
    currentPage = 0;
    loadTableData(currentPage, pageSize, whereCondition);
}

function clearFilter() {
    document.getElementById('where-condition').value = '';
    whereCondition = '';
    currentPage = 0;
    loadTableData(currentPage, pageSize, whereCondition);
}

// 回车键应用过滤
document.getElementById('where-condition').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        applyFilter();
    }
});

document.addEventListener('DOMContentLoaded', () => {
    loadTableData(currentPage, pageSize, whereCondition);
});
</script>
{% endblock %}'''

        with open("templates/table_detail.html", "w", encoding="utf-8") as f:
            f.write(table_detail_template)

    def create_static_files(self):
        """创建静态文件"""
        # 这里可以添加自定义CSS和JS文件
        pass

    def run(self, host: str = "127.0.0.1", port: int = 8080):
        """启动Web服务"""
        print("🌐 启动Yuan数据库管理Web界面")
        print(f"📍 访问地址: http://{host}:{port}")
        print("📋 功能特性:")
        print("   - 📊 数据库概览和统计")
        print("   - 🔍 表数据查看和过滤")
        print("   - 💾 数据库备份")
        print("   - 🧹 数据库清理")
        print("   - 📱 响应式Web界面")

        uvicorn.run(self.app, host=host, port=port, log_level="info")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="Yuan数据库管理Web界面")
    parser.add_argument("--host", default="127.0.0.1", help="绑定主机地址")
    parser.add_argument("--port", type=int, default=8080, help="绑定端口")

    args = parser.parse_args()

    web_manager = WebDatabaseManager()
    web_manager.run(args.host, args.port)


if __name__ == "__main__":
    main()
