<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Yuan数据库管理{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar { min-height: 100vh; background-color: #f8f9fa; }
        .table-container { max-height: 600px; overflow-y: auto; }
        .loading { display: none; }
        .error { color: #dc3545; }
        .success { color: #198754; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <h5><i class="bi bi-database"></i> Yuan数据库</h5>
                    <ul class="nav flex-column" id="database-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/"><i class="bi bi-house"></i> 首页</a>
                        </li>
                    </ul>
                </div>
            </nav>
            <main class="col-md-10 ms-sm-auto px-md-4">
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 加载数据库列表到侧边栏
        async function loadDatabaseNav() {
            try {
                const response = await fetch('/api/databases');
                const result = await response.json();
                if (result.success) {
                    const nav = document.getElementById('database-nav');
                    result.data.forEach(db => {
                        const li = document.createElement('li');
                        li.className = 'nav-item';
                        li.innerHTML = `<a class="nav-link" href="/database/${db.service}">
                            <i class="bi bi-server"></i> ${db.service}
                        </a>`;
                        nav.appendChild(li);
                    });
                }
            } catch (error) {
                console.error('加载数据库列表失败:', error);
            }
        }
        
        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', loadDatabaseNav);
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>