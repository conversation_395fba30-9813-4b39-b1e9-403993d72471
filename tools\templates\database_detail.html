{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-server"></i> 数据库: {{ service }}</h1>
    <div>
        <button class="btn btn-outline-primary" onclick="backupDatabase()">
            <i class="bi bi-download"></i> 备份
        </button>
        <button class="btn btn-outline-warning" onclick="vacuumDatabase()">
            <i class="bi bi-gear"></i> 清理
        </button>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-info-circle"></i> 数据库信息</h5>
            </div>
            <div class="card-body" id="db-info">
                <div class="loading text-center">
                    <div class="spinner-border" role="status"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-table"></i> 数据表</h5>
            </div>
            <div class="card-body" id="tables-list">
                <div class="loading text-center">
                    <div class="spinner-border" role="status"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
const SERVICE = '{{ service }}';

async function loadDatabaseInfo() {
    try {
        const response = await fetch(`/api/database/${SERVICE}`);
        const result = await response.json();

        const infoDiv = document.getElementById('db-info');
        const tablesDiv = document.getElementById('tables-list');

        if (result.success) {
            const info = result.data;

            // 显示数据库信息
            infoDiv.innerHTML = `
                <p><strong>路径:</strong><br><small>${info.path}</small></p>
                <p><strong>大小:</strong> ${info.size_formatted}</p>
                <p><strong>修改时间:</strong><br><small>${new Date(info.modified).toLocaleString()}</small></p>
                <p><strong>表数量:</strong> ${Object.keys(info.tables).length}</p>
            `;

            // 显示表列表
            if (Object.keys(info.tables).length === 0) {
                tablesDiv.innerHTML = '<p class="text-muted">无数据表</p>';
            } else {
                const table = document.createElement('table');
                table.className = 'table table-hover';
                table.innerHTML = `
                    <thead>
                        <tr>
                            <th>表名</th>
                            <th>记录数</th>
                            <th>列数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${Object.entries(info.tables).map(([tableName, tableInfo]) => `
                            <tr>
                                <td><a href="/database/${SERVICE}/table/${tableName}">${tableName}</a></td>
                                <td>${tableInfo.count}</td>
                                <td>${tableInfo.columns.length}</td>
                                <td>
                                    <a href="/database/${SERVICE}/table/${tableName}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i> 查看
                                    </a>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                `;
                tablesDiv.appendChild(table);
            }
        } else {
            infoDiv.innerHTML = `<p class="error">加载失败: ${result.error}</p>`;
            tablesDiv.innerHTML = '';
        }
    } catch (error) {
        document.getElementById('db-info').innerHTML = `<p class="error">网络错误: ${error.message}</p>`;
    }
}

async function backupDatabase() {
    if (!confirm(`确认备份数据库 ${SERVICE}?`)) return;

    try {
        const response = await fetch(`/api/database/${SERVICE}/backup`, {method: 'POST'});
        const result = await response.json();

        if (result.success) {
            alert(`备份成功: ${result.backup_file}`);
        } else {
            alert(`备份失败: ${result.error}`);
        }
    } catch (error) {
        alert(`备份失败: ${error.message}`);
    }
}

async function vacuumDatabase() {
    if (!confirm(`确认清理数据库 ${SERVICE}? 此操作会锁定数据库。`)) return;

    try {
        const response = await fetch(`/api/database/${SERVICE}/vacuum`, {method: 'POST'});
        const result = await response.json();

        if (result.success) {
            alert('数据库清理完成');
            loadDatabaseInfo();
        } else {
            alert(`清理失败: ${result.error}`);
        }
    } catch (error) {
        alert(`清理失败: ${error.message}`);
    }
}

document.addEventListener('DOMContentLoaded', loadDatabaseInfo);
</script>
{% endblock %}