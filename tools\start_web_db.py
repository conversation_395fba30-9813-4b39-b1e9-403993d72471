#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动数据库Web管理工具的便捷脚本
"""

import sys
import webbrowser
import time
from web_db_manager import WebDatabaseManager


def main():
    """启动Web数据库管理工具"""
    print("🚀 启动Yuan数据库Web管理工具")
    print("=" * 50)
    
    # 创建Web管理器
    web_manager = WebDatabaseManager()
    
    # 启动提示
    host = "127.0.0.1"
    port = 8080
    url = f"http://{host}:{port}"
    
    print(f"🌐 Web界面地址: {url}")
    print("📋 功能特性:")
    print("   - 📊 数据库概览和统计信息")
    print("   - 🔍 表数据查看和搜索过滤")
    print("   - 📄 分页浏览大量数据")
    print("   - 💾 一键数据库备份")
    print("   - 🧹 数据库清理优化")
    print("   - 📱 响应式设计，支持移动端")
    print()
    print("💡 使用提示:")
    print("   - 点击数据库名称查看详细信息")
    print("   - 点击表名查看表数据")
    print("   - 使用WHERE条件过滤数据")
    print("   - 支持分页浏览大表数据")
    print()
    print("⚠️ 注意事项:")
    print("   - 备份操作是安全的，不会影响原数据")
    print("   - 清理操作会锁定数据库，建议在服务停止时执行")
    print("   - 大表查询时建议使用WHERE条件过滤")
    print()
    print("🔗 正在自动打开浏览器...")
    
    # 延迟打开浏览器
    def open_browser():
        time.sleep(2)
        try:
            webbrowser.open(url)
            print(f"✅ 浏览器已打开: {url}")
        except Exception as e:
            print(f"❌ 无法自动打开浏览器: {e}")
            print(f"请手动访问: {url}")
    
    import threading
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    print("\n按 Ctrl+C 停止服务")
    print("=" * 50)
    
    try:
        # 启动Web服务
        web_manager.run(host, port)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
