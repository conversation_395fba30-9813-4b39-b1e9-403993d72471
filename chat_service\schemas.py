#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI对话服务API模型定义
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


class ChatMessage(BaseModel):
    """对话消息模型"""
    role: str = Field(..., description="消息角色", pattern="^(user|assistant|system)$")
    content: str = Field(..., description="消息内容")
    metadata: Dict = Field(default_factory=dict, description="消息元数据")


class ChatRequest(BaseModel):
    """对话请求模型"""
    conversation_id: Optional[str] = Field(None, description="对话ID，不提供则创建新对话")
    message: str = Field(..., description="用户消息内容")
    user_id: Optional[str] = Field(None, description="用户ID")
    stream: bool = Field(True, description="是否使用流式响应")
    
    # AI参数
    model: Optional[str] = Field(None, description="AI模型名称")
    temperature: Optional[float] = Field(None, description="随机性控制", ge=0, le=2)
    max_tokens: Optional[int] = Field(None, description="最大生成token数")
    top_p: Optional[float] = Field(None, description="核采样", ge=0, le=1)
    frequency_penalty: Optional[float] = Field(None, description="频率惩罚", ge=-2, le=2)
    presence_penalty: Optional[float] = Field(None, description="存在惩罚", ge=-2, le=2)


class ChatResponse(BaseModel):
    """对话响应模型"""
    success: bool = Field(..., description="操作是否成功")
    data: Dict = Field(..., description="响应数据")
    message: str = Field(..., description="响应消息")
    timestamp: float = Field(..., description="响应时间戳")


class ConversationCreateRequest(BaseModel):
    """创建对话请求模型"""
    title: Optional[str] = Field(None, description="对话标题")
    user_id: Optional[str] = Field(None, description="用户ID")
    metadata: Dict = Field(default_factory=dict, description="对话元数据")


class ConversationUpdateRequest(BaseModel):
    """更新对话请求模型"""
    title: Optional[str] = Field(None, description="对话标题")
    metadata: Dict = Field(default_factory=dict, description="对话元数据")


class ConversationInfo(BaseModel):
    """对话信息模型"""
    id: int = Field(..., description="数据库ID")
    conversation_id: str = Field(..., description="对话ID")
    title: Optional[str] = Field(None, description="对话标题")
    user_id: Optional[str] = Field(None, description="用户ID")
    created_at: float = Field(..., description="创建时间")
    updated_at: float = Field(..., description="更新时间")
    message_count: int = Field(..., description="消息数量")
    metadata: Dict = Field(..., description="对话元数据")


class MessageInfo(BaseModel):
    """消息信息模型"""
    id: int = Field(..., description="数据库ID")
    conversation_id: str = Field(..., description="对话ID")
    role: str = Field(..., description="消息角色")
    content: str = Field(..., description="消息内容")
    created_at: float = Field(..., description="创建时间")
    metadata: Dict = Field(..., description="消息元数据")


class ConversationListResponse(BaseModel):
    """对话列表响应模型"""
    success: bool = Field(..., description="操作是否成功")
    data: Dict[str, Any] = Field(..., description="对话列表数据")
    message: str = Field(..., description="响应消息")
    timestamp: float = Field(..., description="响应时间戳")


class MessageListResponse(BaseModel):
    """消息列表响应模型"""
    success: bool = Field(..., description="操作是否成功")
    data: Dict[str, Any] = Field(..., description="消息列表数据")
    message: str = Field(..., description="响应消息")
    timestamp: float = Field(..., description="响应时间戳")


class ChatStatsResponse(BaseModel):
    """对话统计响应模型"""
    success: bool = Field(..., description="操作是否成功")
    data: Dict[str, Any] = Field(..., description="统计数据")
    message: str = Field(..., description="响应消息")
    timestamp: float = Field(..., description="响应时间戳")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = Field(False, description="操作是否成功")
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误描述")
    details: Dict = Field(default_factory=dict, description="错误详情")
    timestamp: float = Field(..., description="响应时间戳")
