{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-speedometer2"></i> 数据库管理面板</h1>
    <button class="btn btn-primary" onclick="refreshDatabases()">
        <i class="bi bi-arrow-clockwise"></i> 刷新
    </button>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-list"></i> 数据库列表</h5>
            </div>
            <div class="card-body">
                <div id="loading" class="loading text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
                <div id="database-list"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
async function loadDatabases() {
    const loading = document.getElementById('loading');
    const list = document.getElementById('database-list');
    
    loading.style.display = 'block';
    list.innerHTML = '';
    
    try {
        const response = await fetch('/api/databases');
        const result = await response.json();
        
        if (result.success) {
            if (result.data.length === 0) {
                list.innerHTML = '<p class="text-muted">未发现任何数据库</p>';
            } else {
                const table = document.createElement('table');
                table.className = 'table table-striped';
                table.innerHTML = `
                    <thead>
                        <tr>
                            <th>服务名称</th>
                            <th>文件大小</th>
                            <th>表数量</th>
                            <th>总记录数</th>
                            <th>修改时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${result.data.map(db => `
                            <tr>
                                <td><a href="/database/${db.service}">${db.service}</a></td>
                                <td>${db.size_formatted || 'N/A'}</td>
                                <td>${db.table_count || 'N/A'}</td>
                                <td>${db.total_records || 'N/A'}</td>
                                <td>${db.modified ? new Date(db.modified).toLocaleString() : 'N/A'}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="backupDatabase('${db.service}')">
                                        <i class="bi bi-download"></i> 备份
                                    </button>
                                    <button class="btn btn-sm btn-outline-warning" onclick="vacuumDatabase('${db.service}')">
                                        <i class="bi bi-gear"></i> 清理
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                `;
                list.appendChild(table);
            }
        } else {
            list.innerHTML = `<p class="error">加载失败: ${result.error}</p>`;
        }
    } catch (error) {
        list.innerHTML = `<p class="error">网络错误: ${error.message}</p>`;
    } finally {
        loading.style.display = 'none';
    }
}

async function backupDatabase(service) {
    if (!confirm(`确认备份数据库 ${service}?`)) return;
    
    try {
        const response = await fetch(`/api/database/${service}/backup`, {method: 'POST'});
        const result = await response.json();
        
        if (result.success) {
            alert(`备份成功: ${result.backup_file}`);
        } else {
            alert(`备份失败: ${result.error}`);
        }
    } catch (error) {
        alert(`备份失败: ${error.message}`);
    }
}

async function vacuumDatabase(service) {
    if (!confirm(`确认清理数据库 ${service}? 此操作会锁定数据库。`)) return;
    
    try {
        const response = await fetch(`/api/database/${service}/vacuum`, {method: 'POST'});
        const result = await response.json();
        
        if (result.success) {
            alert('数据库清理完成');
            refreshDatabases();
        } else {
            alert(`清理失败: ${result.error}`);
        }
    } catch (error) {
        alert(`清理失败: ${error.message}`);
    }
}

function refreshDatabases() {
    loadDatabases();
}

// 页面加载时执行
document.addEventListener('DOMContentLoaded', loadDatabases);
</script>
{% endblock %}