#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI对话服务数据模型
"""

import time
from typing import Dict, List, Optional
from sqlalchemy import create_engine, Column, Integer, String, Text, Float, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from .config import config

# 数据库基类
Base = declarative_base()


class Conversation(Base):
    """对话会话模型"""
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    conversation_id = Column(String(50), unique=True, nullable=False, index=True)
    title = Column(String(200), nullable=True)
    user_id = Column(String(50), nullable=True, index=True)
    created_at = Column(Float, nullable=False, default=time.time)
    updated_at = Column(Float, nullable=False, default=time.time)
    message_count = Column(Integer, default=0)
    extra_data = Column(JSON, default=dict)
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "id": self.id,
            "conversation_id": self.conversation_id,
            "title": self.title,
            "user_id": self.user_id,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "message_count": self.message_count,
            "metadata": self.extra_data
        }


class Message(Base):
    """对话消息模型"""
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    conversation_id = Column(String(50), nullable=False, index=True)
    role = Column(String(20), nullable=False)  # user, assistant, system
    content = Column(Text, nullable=False)
    created_at = Column(Float, nullable=False, default=time.time)
    extra_data = Column(JSON, default=dict)
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "id": self.id,
            "conversation_id": self.conversation_id,
            "role": self.role,
            "content": self.content,
            "created_at": self.created_at,
            "metadata": self.extra_data
        }


class ChatDatabase:
    """对话数据库管理类"""
    
    def __init__(self, database_url: str = None):
        self.database_url = database_url or config.database_url
        
        # 确保data目录存在
        import os
        os.makedirs("data", exist_ok=True)
        
        # 创建数据库引擎
        self.engine = create_engine(self.database_url, echo=False)
        
        # 创建会话工厂
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # 创建表
        Base.metadata.create_all(bind=self.engine)
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return self.SessionLocal()
    
    def create_conversation(self, conversation_id: str, title: str = None, user_id: str = None, metadata: Dict = None) -> Conversation:
        """创建新对话"""
        with self.get_session() as session:
            conversation = Conversation(
                conversation_id=conversation_id,
                title=title,
                user_id=user_id,
                extra_data=metadata or {}
            )
            session.add(conversation)
            session.commit()
            session.refresh(conversation)
            return conversation
    
    def get_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """获取对话"""
        with self.get_session() as session:
            return session.query(Conversation).filter(
                Conversation.conversation_id == conversation_id
            ).first()
    
    def update_conversation(self, conversation_id: str, title: str = None, metadata: Dict = None) -> bool:
        """更新对话"""
        with self.get_session() as session:
            conversation = session.query(Conversation).filter(
                Conversation.conversation_id == conversation_id
            ).first()
            
            if not conversation:
                return False
            
            if title is not None:
                conversation.title = title
            if metadata is not None:
                conversation.extra_data.update(metadata)
            
            conversation.updated_at = time.time()
            session.commit()
            return True
    
    def delete_conversation(self, conversation_id: str) -> bool:
        """删除对话及其所有消息"""
        with self.get_session() as session:
            # 删除消息
            session.query(Message).filter(
                Message.conversation_id == conversation_id
            ).delete()
            
            # 删除对话
            deleted = session.query(Conversation).filter(
                Conversation.conversation_id == conversation_id
            ).delete()
            
            session.commit()
            return deleted > 0
    
    def add_message(self, conversation_id: str, role: str, content: str, metadata: Dict = None) -> Message:
        """添加消息"""
        with self.get_session() as session:
            message = Message(
                conversation_id=conversation_id,
                role=role,
                content=content,
                extra_data=metadata or {}
            )
            session.add(message)
            
            # 更新对话的消息计数和更新时间
            conversation = session.query(Conversation).filter(
                Conversation.conversation_id == conversation_id
            ).first()
            
            if conversation:
                conversation.message_count += 1
                conversation.updated_at = time.time()
            
            session.commit()
            session.refresh(message)
            return message
    
    def get_messages(self, conversation_id: str, limit: int = None) -> List[Message]:
        """获取对话消息"""
        with self.get_session() as session:
            query = session.query(Message).filter(
                Message.conversation_id == conversation_id
            ).order_by(Message.created_at)
            
            if limit:
                query = query.limit(limit)
            
            return query.all()
    
    def get_conversations(self, user_id: str = None, limit: int = 50) -> List[Conversation]:
        """获取对话列表"""
        with self.get_session() as session:
            query = session.query(Conversation).order_by(Conversation.updated_at.desc())
            
            if user_id:
                query = query.filter(Conversation.user_id == user_id)
            
            return query.limit(limit).all()
    
    def get_conversation_stats(self) -> Dict:
        """获取对话统计信息"""
        with self.get_session() as session:
            total_conversations = session.query(Conversation).count()
            total_messages = session.query(Message).count()
            
            return {
                "total_conversations": total_conversations,
                "total_messages": total_messages
            }
