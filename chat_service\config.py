#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI对话服务配置管理
"""

from typing import Optional
from pydantic_settings import BaseSettings


class ChatServiceConfig(BaseSettings):
    """AI对话服务配置"""
    
    # 服务基本配置
    service_name: str = "chat_service"
    service_port: int = 8002
    service_host: str = "127.0.0.1"
    
    # 数据库配置
    database_url: str = "sqlite:///data/chat_service.db"
    
    # AI配置
    openai_api_key: str = "sk-r19hLjYZpJwzPFRp1Al98fVVCdyyHWImBXUQbLNnH2YrA1Pv"
    openai_base_url: str = "https://xiaoai.plus/v1"
    default_model: str = "gpt-4o"
    
    # 对话配置
    max_tokens: Optional[int] = None
    temperature: float = 0.3
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    timeout: float = 60.0
    
    # 会话配置
    max_conversation_length: int = 100  # 单个会话最大消息数
    conversation_timeout: int = 3600    # 会话超时时间（秒）
    
    # 日志配置
    log_level: str = "INFO"
    
    class Config:
        env_file = ".env"
        env_prefix = "CHAT_"


# 全局配置实例
config = ChatServiceConfig()
