#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理工具
提供数据库查看、维护、备份等功能
"""

import os
import sqlite3
import json
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, data_dir: str = "data"):
        # 如果是相对路径，则相对于项目根目录
        if not os.path.isabs(data_dir):
            # 获取项目根目录（tools的上级目录）
            project_root = Path(__file__).parent.parent
            self.data_dir = project_root / data_dir
        else:
            self.data_dir = Path(data_dir)
        self.databases = self._discover_databases()
    
    def _discover_databases(self) -> Dict[str, str]:
        """发现所有数据库文件"""
        databases = {}
        if self.data_dir.exists():
            for db_file in self.data_dir.glob("*.db"):
                service_name = db_file.stem.replace("_service", "")
                databases[service_name] = str(db_file)
        return databases
    
    def list_databases(self) -> None:
        """列出所有数据库"""
        print("📊 发现的数据库:")
        if not self.databases:
            print("  ❌ 未发现任何数据库文件")
            return
        
        for service, db_path in self.databases.items():
            size = os.path.getsize(db_path) if os.path.exists(db_path) else 0
            size_str = self._format_size(size)
            print(f"  📁 {service}: {db_path} ({size_str})")
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
    
    def get_database_info(self, service: str) -> Dict[str, Any]:
        """获取数据库信息"""
        if service not in self.databases:
            raise ValueError(f"数据库 {service} 不存在")
        
        db_path = self.databases[service]
        if not os.path.exists(db_path):
            raise FileNotFoundError(f"数据库文件不存在: {db_path}")
        
        conn = sqlite3.connect(db_path)
        try:
            cursor = conn.cursor()
            
            # 获取表信息
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            # 获取每个表的记录数
            table_info = {}
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table})")
                columns = [
                    {
                        "name": row[1],
                        "type": row[2],
                        "not_null": bool(row[3]),
                        "primary_key": bool(row[5])
                    }
                    for row in cursor.fetchall()
                ]
                
                table_info[table] = {
                    "count": count,
                    "columns": columns
                }
            
            # 获取文件信息
            file_stat = os.stat(db_path)
            
            return {
                "service": service,
                "path": db_path,
                "size": file_stat.st_size,
                "size_formatted": self._format_size(file_stat.st_size),
                "modified": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                "tables": table_info
            }
        finally:
            conn.close()
    
    def show_database_info(self, service: str) -> None:
        """显示数据库信息"""
        try:
            info = self.get_database_info(service)
            
            print(f"📊 数据库信息: {service}")
            print(f"📁 路径: {info['path']}")
            print(f"📏 大小: {info['size_formatted']}")
            print(f"🕒 修改时间: {info['modified']}")
            print(f"📋 表数量: {len(info['tables'])}")
            print()
            
            for table_name, table_info in info['tables'].items():
                print(f"📊 表: {table_name} ({table_info['count']} 条记录)")
                for col in table_info['columns']:
                    pk_mark = " 🔑" if col['primary_key'] else ""
                    null_mark = " ❌" if col['not_null'] else " ✅"
                    print(f"  - {col['name']}: {col['type']}{pk_mark}{null_mark}")
                print()
                
        except Exception as e:
            print(f"❌ 获取数据库信息失败: {e}")
    
    def query_table(self, service: str, table: str, limit: int = 10, where: str = None) -> List[Dict]:
        """查询表数据"""
        if service not in self.databases:
            raise ValueError(f"数据库 {service} 不存在")

        db_path = self.databases[service]
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以按列名访问

        try:
            cursor = conn.cursor()

            # 构建查询语句
            query = f"SELECT * FROM {table}"
            if where:
                query += f" WHERE {where}"

            # 只有当limit不为None时才添加LIMIT子句
            if limit is not None:
                query += f" LIMIT {limit}"

            cursor.execute(query)
            rows = cursor.fetchall()

            # 转换为字典列表
            return [dict(row) for row in rows]

        finally:
            conn.close()
    
    def show_table_data(self, service: str, table: str, limit: int = 10, where: str = None) -> None:
        """显示表数据"""
        try:
            rows = self.query_table(service, table, limit, where)
            
            if not rows:
                print(f"📊 表 {table} 无数据")
                return
            
            print(f"📊 表 {table} 数据 (显示前{len(rows)}条):")
            
            # 获取列名
            columns = list(rows[0].keys())
            
            # 计算列宽
            col_widths = {}
            for col in columns:
                col_widths[col] = max(
                    len(col),
                    max(len(str(row[col])) for row in rows)
                )
                # 限制最大宽度
                col_widths[col] = min(col_widths[col], 30)
            
            # 打印表头
            header = " | ".join(col.ljust(col_widths[col]) for col in columns)
            print(f"  {header}")
            print(f"  {'-' * len(header)}")
            
            # 打印数据行
            for row in rows:
                row_str = " | ".join(
                    str(row[col])[:col_widths[col]].ljust(col_widths[col])
                    for col in columns
                )
                print(f"  {row_str}")
            
        except Exception as e:
            print(f"❌ 查询表数据失败: {e}")
    
    def backup_database(self, service: str, backup_dir: str = "backups") -> str:
        """备份数据库"""
        if service not in self.databases:
            raise ValueError(f"数据库 {service} 不存在")
        
        # 创建备份目录
        backup_path = Path(backup_dir)
        backup_path.mkdir(exist_ok=True)
        
        # 生成备份文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = backup_path / f"{service}_backup_{timestamp}.db"
        
        # 复制数据库文件
        source_path = self.databases[service]
        
        import shutil
        shutil.copy2(source_path, backup_file)
        
        print(f"✅ 数据库备份完成: {backup_file}")
        return str(backup_file)
    
    def vacuum_database(self, service: str) -> None:
        """清理数据库（VACUUM操作）"""
        if service not in self.databases:
            raise ValueError(f"数据库 {service} 不存在")
        
        db_path = self.databases[service]
        
        # 获取清理前的大小
        old_size = os.path.getsize(db_path)
        
        conn = sqlite3.connect(db_path)
        try:
            cursor = conn.cursor()
            cursor.execute("VACUUM")
            conn.commit()
        finally:
            conn.close()
        
        # 获取清理后的大小
        new_size = os.path.getsize(db_path)
        saved = old_size - new_size
        
        print(f"✅ 数据库清理完成")
        print(f"📏 原大小: {self._format_size(old_size)}")
        print(f"📏 新大小: {self._format_size(new_size)}")
        print(f"💾 节省空间: {self._format_size(saved)}")


def main():
    """主函数 - 交互式数据库管理"""
    manager = DatabaseManager()
    
    print("🛠️ Yuan项目数据库管理工具")
    print("=" * 40)
    
    while True:
        print("\n📋 可用操作:")
        print("  1. 列出所有数据库")
        print("  2. 查看数据库信息")
        print("  3. 查看表数据")
        print("  4. 备份数据库")
        print("  5. 清理数据库")
        print("  0. 退出")
        
        try:
            choice = input("\n请选择操作 (0-5): ").strip()
            
            if choice == "0":
                print("👋 再见！")
                break
            elif choice == "1":
                manager.list_databases()
            elif choice == "2":
                service = input("请输入服务名称: ").strip()
                manager.show_database_info(service)
            elif choice == "3":
                service = input("请输入服务名称: ").strip()
                table = input("请输入表名: ").strip()
                limit = input("显示记录数 (默认10): ").strip()
                limit = int(limit) if limit else 10
                where = input("WHERE条件 (可选): ").strip()
                where = where if where else None
                manager.show_table_data(service, table, limit, where)
            elif choice == "4":
                service = input("请输入服务名称: ").strip()
                manager.backup_database(service)
            elif choice == "5":
                service = input("请输入服务名称: ").strip()
                confirm = input(f"确认清理数据库 {service}? (y/N): ").strip().lower()
                if confirm == 'y':
                    manager.vacuum_database(service)
                else:
                    print("❌ 操作已取消")
            else:
                print("❌ 无效选择，请重试")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")


if __name__ == "__main__":
    main()
