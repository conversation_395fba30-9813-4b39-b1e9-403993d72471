#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Yuan项目启动脚本
管理微服务集群的启动和停止
"""

import asyncio
import subprocess
import time
import sys
import signal
from pathlib import Path


class YuanServiceManager:
    """Yuan微服务管理器"""
    
    def __init__(self):
        self.services = [
            {
                "name": "service_registry",
                "script": "service_registry_service/main.py",
                "port": 8500,
                "process": None,
                "description": "服务注册中心",
                "required": True
            },
            {
                "name": "chat_service",
                "script": "chat_service/main.py",
                "port": 8002,
                "process": None,
                "description": "AI对话服务",
                "required": False
            }
            # 其他服务将在实现后添加到这里
        ]
        self.base_dir = Path(__file__).parent
    
    def start_service(self, service: dict):
        """启动单个服务"""
        script_path = self.base_dir / service["script"]
        
        print(f"🚀 启动 {service['description']} (端口 {service['port']})...")
        
        try:
            # 使用模块方式启动服务
            if service["name"] == "service_registry":
                process = subprocess.Popen(
                    [sys.executable, "-m", "service_registry_service.main"],
                    cwd=self.base_dir,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if sys.platform == "win32" else 0
                )
            elif service["name"] == "chat_service":
                process = subprocess.Popen(
                    [sys.executable, "-m", "chat_service.main"],
                    cwd=self.base_dir,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if sys.platform == "win32" else 0
                )
            else:
                process = subprocess.Popen(
                    [sys.executable, str(script_path)],
                    cwd=self.base_dir,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if sys.platform == "win32" else 0
                )
            
            service["process"] = process
            print(f"✅ {service['description']} 启动成功 (PID: {process.pid})")
            return True
            
        except Exception as e:
            print(f"❌ {service['description']} 启动失败: {e}")
            return False
    
    def stop_service(self, service: dict):
        """停止单个服务"""
        if service["process"] and service["process"].poll() is None:
            print(f"🛑 停止 {service['description']}...")
            
            try:
                if sys.platform == "win32":
                    subprocess.run(
                        ["taskkill", "/F", "/T", "/PID", str(service["process"].pid)],
                        check=False
                    )
                else:
                    service["process"].terminate()
                    service["process"].wait(timeout=5)
                
                print(f"✅ {service['description']} 已停止")
                
            except Exception as e:
                print(f"⚠️ 停止 {service['description']} 时出错: {e}")
            
            service["process"] = None
    
    def start_all(self):
        """启动所有服务"""
        print("🌟 启动Yuan微服务集群")
        print("=" * 60)
        print("基于服务注册中心的微服务架构")
        print("=" * 60)
        
        for service in self.services:
            if self.start_service(service):
                # 等待服务启动
                if service["name"] == "service_registry":
                    time.sleep(3)  # 注册中心需要更多时间
                else:
                    time.sleep(2)
            else:
                print("❌ 服务启动失败，停止启动流程")
                self.stop_all()
                return False
        
        print("\n" + "=" * 60)
        print("🎉 Yuan微服务集群启动完成！")
        print("\n📍 服务地址:")
        for service in self.services:
            print(f"   {service['description']}: http://127.0.0.1:{service['port']}")
        
        print("\n🏛️ 服务注册中心: http://127.0.0.1:8500")
        print("📚 API文档: http://127.0.0.1:8500/docs")
        print("🔍 健康检查: http://127.0.0.1:8500/health")
        print("📋 服务列表: http://127.0.0.1:8500/services")
        
        print("\n🚀 开发指南:")
        print("   1. 新增服务请参考: docs/开发规范.md")
        print("   2. 端口分配规则: docs/服务端口分配.md")
        print("   3. 架构设计文档: docs/项目设计/07-通讯架构设计.md")
        
        print("\n按 Ctrl+C 停止所有服务")
        
        return True
    
    def stop_all(self):
        """停止所有服务"""
        print("\n🛑 停止所有微服务...")
        
        # 反向停止服务
        for service in reversed(self.services):
            self.stop_service(service)
        
        print("✅ 所有微服务已停止")
    
    def check_services(self):
        """检查服务状态"""
        print("\n📊 服务状态检查:")
        for service in self.services:
            if service["process"] and service["process"].poll() is None:
                print(f"   ✅ {service['description']}: 运行中 (PID: {service['process'].pid})")
            else:
                print(f"   ❌ {service['description']}: 已停止")
    
    def run(self):
        """运行服务管理器"""
        def signal_handler(signum, frame):
            print("\n收到停止信号...")
            self.stop_all()
            sys.exit(0)
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            if self.start_all():
                # 保持运行状态
                while True:
                    time.sleep(5)
                    
                    # 检查服务是否还在运行
                    all_running = True
                    for service in self.services:
                        if not service["process"] or service["process"].poll() is not None:
                            print(f"⚠️ 检测到 {service['description']} 异常退出")
                            all_running = False
                    
                    if not all_running:
                        print("❌ 部分服务异常，停止所有服务")
                        break
            
        except KeyboardInterrupt:
            print("\n用户中断...")
        except Exception as e:
            print(f"\n❌ 运行时错误: {e}")
        finally:
            self.stop_all()


def main():
    """主函数"""
    print("🔧 Yuan项目微服务管理器")
    print("基于服务注册中心的微服务架构")
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "start":
            manager = YuanServiceManager()
            manager.run()
        elif command == "stop":
            print("停止功能需要在运行中的管理器中使用 Ctrl+C")
        elif command == "status":
            manager = YuanServiceManager()
            manager.check_services()
        elif command == "help":
            print("\n用法:")
            print("  python start_yuan.py [start|stop|status|help]")
            print("\n命令说明:")
            print("  start  - 启动所有微服务")
            print("  stop   - 停止所有微服务")
            print("  status - 检查服务状态")
            print("  help   - 显示帮助信息")
            print("\n开发文档:")
            print("  - 微服务开发规范: docs/开发规范.md")
            print("  - 服务端口分配: docs/服务端口分配.md")
            print("  - 架构设计文档: docs/项目设计/07-通讯架构设计.md")
        else:
            print("用法: python start_yuan.py [start|stop|status|help]")
    else:
        # 默认启动所有服务
        manager = YuanServiceManager()
        manager.run()


if __name__ == "__main__":
    main()
