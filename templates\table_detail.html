{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-table"></i> 表: {{ service }}.{{ table }}</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">首页</a></li>
            <li class="breadcrumb-item"><a href="/database/{{ service }}">{{ service }}</a></li>
            <li class="breadcrumb-item active">{{ table }}</li>
        </ol>
    </nav>
</div>

<div class="row mb-3">
    <div class="col-md-6">
        <div class="input-group">
            <input type="text" class="form-control" id="where-condition" placeholder="WHERE条件 (例: id > 10)">
            <button class="btn btn-outline-secondary" onclick="applyFilter()">
                <i class="bi bi-funnel"></i> 过滤
            </button>
            <button class="btn btn-outline-secondary" onclick="clearFilter()">
                <i class="bi bi-x"></i> 清除
            </button>
        </div>
    </div>
    <div class="col-md-6 text-end">
        <select class="form-select d-inline-block w-auto" id="page-size" onchange="changePageSize()">
            <option value="10">10条/页</option>
            <option value="25" selected>25条/页</option>
            <option value="50">50条/页</option>
            <option value="100">100条/页</option>
        </select>
    </div>
</div>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5><i class="bi bi-list-ul"></i> 表数据</h5>
        <span id="record-info" class="text-muted"></span>
    </div>
    <div class="card-body">
        <div id="loading" class="loading text-center">
            <div class="spinner-border" role="status"></div>
        </div>
        <div class="table-container">
            <div id="table-data"></div>
        </div>
        <nav id="pagination" class="mt-3"></nav>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
const SERVICE = '{{ service }}';
const TABLE = '{{ table }}';
let currentPage = 0;
let pageSize = 25;
let whereCondition = '';
let totalRecords = 0;

async function loadTableData(page = 0, size = 25, where = '') {
    const loading = document.getElementById('loading');
    const tableDiv = document.getElementById('table-data');
    const recordInfo = document.getElementById('record-info');

    loading.style.display = 'block';
    tableDiv.innerHTML = '';

    try {
        const offset = page * size;
        let url = `/api/database/${SERVICE}/table/${TABLE}?limit=${size}&offset=${offset}`;
        if (where) {
            url += `&where=${encodeURIComponent(where)}`;
        }

        const response = await fetch(url);
        const result = await response.json();

        if (result.success) {
            const data = result.data;
            totalRecords = data.total;

            // 更新记录信息
            const start = offset + 1;
            const end = Math.min(offset + size, totalRecords);
            recordInfo.textContent = `显示 ${start}-${end} / 共 ${totalRecords} 条记录`;

            if (data.rows.length === 0) {
                tableDiv.innerHTML = '<p class="text-muted">无数据</p>';
            } else {
                // 创建表格
                const table = document.createElement('table');
                table.className = 'table table-striped table-hover';

                // 表头
                const columns = Object.keys(data.rows[0]);
                const thead = document.createElement('thead');
                thead.innerHTML = `<tr>${columns.map(col => `<th>${col}</th>`).join('')}</tr>`;
                table.appendChild(thead);

                // 表体
                const tbody = document.createElement('tbody');
                data.rows.forEach(row => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = columns.map(col => {
                        let value = row[col];
                        if (value === null) value = '<span class="text-muted">NULL</span>';
                        else if (typeof value === 'string' && value.length > 50) {
                            value = value.substring(0, 50) + '...';
                        }
                        return `<td>${value}</td>`;
                    }).join('');
                    tbody.appendChild(tr);
                });
                table.appendChild(tbody);

                tableDiv.appendChild(table);
            }

            // 更新分页
            updatePagination(page, size, totalRecords);

        } else {
            tableDiv.innerHTML = `<p class="error">加载失败: ${result.error}</p>`;
        }
    } catch (error) {
        tableDiv.innerHTML = `<p class="error">网络错误: ${error.message}</p>`;
    } finally {
        loading.style.display = 'none';
    }
}

function updatePagination(page, size, total) {
    const pagination = document.getElementById('pagination');
    const totalPages = Math.ceil(total / size);

    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let html = '<ul class="pagination justify-content-center">';

    // 上一页
    if (page > 0) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${page - 1})">上一页</a></li>`;
    } else {
        html += '<li class="page-item disabled"><span class="page-link">上一页</span></li>';
    }

    // 页码
    const startPage = Math.max(0, page - 2);
    const endPage = Math.min(totalPages - 1, page + 2);

    if (startPage > 0) {
        html += '<li class="page-item"><a class="page-link" href="#" onclick="goToPage(0)">1</a></li>';
        if (startPage > 1) html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
    }

    for (let i = startPage; i <= endPage; i++) {
        if (i === page) {
            html += `<li class="page-item active"><span class="page-link">${i + 1}</span></li>`;
        } else {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${i})">${i + 1}</a></li>`;
        }
    }

    if (endPage < totalPages - 1) {
        if (endPage < totalPages - 2) html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${totalPages - 1})">${totalPages}</a></li>`;
    }

    // 下一页
    if (page < totalPages - 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${page + 1})">下一页</a></li>`;
    } else {
        html += '<li class="page-item disabled"><span class="page-link">下一页</span></li>';
    }

    html += '</ul>';
    pagination.innerHTML = html;
}

function goToPage(page) {
    currentPage = page;
    loadTableData(currentPage, pageSize, whereCondition);
}

function changePageSize() {
    pageSize = parseInt(document.getElementById('page-size').value);
    currentPage = 0;
    loadTableData(currentPage, pageSize, whereCondition);
}

function applyFilter() {
    whereCondition = document.getElementById('where-condition').value.trim();
    currentPage = 0;
    loadTableData(currentPage, pageSize, whereCondition);
}

function clearFilter() {
    document.getElementById('where-condition').value = '';
    whereCondition = '';
    currentPage = 0;
    loadTableData(currentPage, pageSize, whereCondition);
}

// 回车键应用过滤
document.getElementById('where-condition').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        applyFilter();
    }
});

document.addEventListener('DOMContentLoaded', () => {
    loadTableData(currentPage, pageSize, whereCondition);
});
</script>
{% endblock %}