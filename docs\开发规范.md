# Yuan项目开发规范

## 项目特性
本项目为局域网内的前沿探索实验性项目，以功能实现、简洁有效为开发目标。

## 核心开发原则

### 功能导向开发
- 以实现功能为首要目标
- 代码简洁有效，避免过度设计
- 快速迭代，持续改进
- 实用性优于理论完美

### 文档驱动开发
- **所有模块开发时，必须创建功能文档和API文档**
- **随着开发进度，实时更新功能清单和API清单**
- 文档与代码同步维护，方便排查重复问题
- 每个模块的文档包括：功能说明、API接口、使用示例

### 模块化无重复原则
- **每个模块原则上不得有重复功能**
- 开发前必须检查现有模块，避免重复开发
- 相似功能应整合到同一模块中
- 模块间通过明确的接口进行通信

### 单一职责原则
- 每个模块、类、函数都有明确的单一职责
- 一个类只负责一个业务领域
- 一个函数只实现一个具体功能
- 避免"万能"类和函数

## 微服务架构规范

### 总体架构原则
Yuan项目采用基于服务注册中心的微服务架构，每个服务必须遵循统一的开发规范，确保服务间的一致性和可维护性。

### 服务结构规范
- **服务命名**：小写字母，使用下划线分隔，如`chat_service`、`log_service`
- **目录结构**：每个服务包含main.py、service.py、models.py、schemas.py、config.py等标准文件
- **数据库独立**：每个服务使用独立的SQLite数据库，命名格式：`{service_name}_service.db`
- **端口分配**：按功能分配固定端口号，避免冲突

### 服务基础实现
- **继承BaseService**：所有微服务必须继承`BaseService`类
- **服务注册**：启动时自动注册到服务注册中心
- **标准端点**：必须实现`/health`和`/info`端点
- **统一响应格式**：使用标准的JSON响应格式

### 服务间通信
- **服务发现**：通过服务注册中心发现其他服务
- **API调用**：使用BaseService提供的统一调用方法
- **错误处理**：实现完善的错误处理和日志记录机制

## 数据库设计规范

### 独立数据库原则
- 每个服务使用独立的SQLite数据库
- 数据库文件存储在`data/`目录下
- 命名格式：`{service_name}_service.db`
- 服务间不得直接访问其他服务的数据库

### 数据库模型定义
- 使用SQLAlchemy定义数据库模型
- 统一的字段命名和类型约定
- 必须包含id、created_at等基础字段
- 合理使用索引提高查询性能

### 数据库初始化
- 服务启动时自动初始化数据库
- 支持数据库结构升级和迁移
- 提供数据备份和恢复机制

## API设计规范

### RESTful设计原则
- 使用标准HTTP方法：GET、POST、PUT、DELETE
- 资源导向的URL设计
- 使用HTTP状态码表示操作结果
- 支持查询参数和分页

### 统一响应格式
- 成功响应：`{"success": true, "data": {}, "message": "操作成功"}`
- 错误响应：`{"success": false, "error": "错误类型", "message": "错误描述"}`
- 包含时间戳和请求ID便于追踪

### 请求/响应模型
- 使用Pydantic定义请求和响应模型
- 严格的类型检查和数据验证
- 清晰的字段说明和示例

## 文档管理规范

### 功能文档要求
- **每个模块必须创建功能文档**，包含：
  - 模块功能描述
  - 主要特性列表
  - 使用场景说明
  - 依赖关系说明
- **实时更新功能清单**，记录所有已实现功能
- 功能文档放置在模块目录下的`README.md`中

### API文档要求
- **每个模块必须创建API文档**，包含：
  - 所有公开接口的详细说明
  - 参数类型和返回值说明
  - 调用示例和错误处理
  - 版本变更记录
  - 如没有需要外部调用的接口，可以写无
- **实时更新API清单**，方便其他模块调用
- API文档放置在模块目录下的`API.md`中

### 重复功能检查
- 开发新功能前，必须检查现有功能清单
- 发现相似功能时，优先扩展现有模块
- 定期审查功能清单，合并重复功能
- 维护全局功能索引，避免重复开发

## 编码标准

### 文件组织
- 代码文件控制在合理长度内，便于维护
- 相关功能的文件放在同一目录下
- 使用清晰的目录结构反映代码架构
- 每个模块包含：代码文件、功能文档、API文档

### 命名规范
- 函数和类采用驼峰命名法
- 变量、函数、类名要有实际意义，能表达其用途
- 避免使用无意义的缩写
- 常量使用全大写字母和下划线

### 函数设计
- 每个函数只负责一个功能，避免臃肿
- 函数参数控制在合理范围内
- 优先选择简洁有效的实现方式
- 注重实用性，避免过度抽象

## 配置管理规范

### 配置文件结构
- 使用Pydantic BaseSettings管理配置
- 支持环境变量覆盖配置
- 配置文件统一命名为config.py
- 敏感配置通过环境变量传递

### 环境变量支持
- 配置项支持环境变量覆盖
- 使用统一的环境变量前缀
- 开发、测试、生产环境配置分离

## 日志记录规范

### 日志级别
- **DEBUG**：调试信息
- **INFO**：一般信息
- **WARNING**：警告信息
- **ERROR**：错误信息
- **CRITICAL**：严重错误

### 日志记录方式
- 通过Log Service统一记录日志
- 包含模块名、函数名、时间戳等信息
- 结构化日志格式，便于分析和查询

## 语言特定规范

### Python规范
- 始终保持使用UTF-8编码
- **必须使用项目全局虚拟环境**：`D:\project\Yuan\venv`
- **任何基于Python的代码，均使用该虚拟环境**
- 激活命令：`.\venv\Scripts\Activate.ps1`
- Python版本：3.10.11
- 使用类型提示提高代码可读性
- 异常处理要具体明确，便于调试

### TypeScript/React规范
- 使用严格的TypeScript配置
- 组件设计简洁，功能单一
- 优先考虑功能实现，适度使用类型约束
- 保持代码风格一致性

## 注释和文档

### 注释原则
- 重点解释业务逻辑和设计思路
- 复杂算法必须有详细注释
- 关键决策点要说明原因
- 临时解决方案要标注TODO和改进计划

### 文档同步更新
- **代码变更时，必须同步更新相关文档**
- 功能增加时，更新功能清单
- API变更时，更新API文档
- 定期检查文档与代码的一致性

## 错误处理

### 异常处理
- 使用具体的异常类型，便于问题定位
- 异常信息要清晰描述问题和可能的解决方案
- 关键操作要有错误恢复机制
- 记录详细的错误日志，包含上下文信息

### 边界条件
- 输入验证要全面，防止程序崩溃
- 妥善处理空值和边界情况
- 网络请求要有超时和重试机制
- 资源使用要有限制和清理机制

## 性能考虑

### 资源管理
- 及时释放不需要的资源
- 避免内存泄漏，特别是长期运行的服务
- 数据库连接要正确关闭
- 文件操作要使用上下文管理器

### 实用性优先
- 选择简单有效的解决方案
- 避免过度优化，专注功能实现
- 性能问题出现时再针对性优化
- 优先保证功能正确性

## 项目特殊考虑

### 局域网实验环境
- **本项目为局域网内的前沿探索实验性项目**
- **无需考虑代码安全和环境风险**
- **无需使用沙箱等安全隔离措施**
- 专注于功能实现和技术探索

### 开发环境要求
- Windows系统下优先使用PowerShell
- Python开发使用虚拟环境
- 执行命令时逐条执行，避免使用&&连接
- 保持UTF-8编码统一性

## 测试要求

### 功能验证
- 核心功能必须经过基本测试验证
- 重点测试主要使用场景
- 边界条件和异常情况要考虑
- 测试以验证功能正确性为主

### 测试原则
- 测试要简单实用，易于执行
- 优先手动测试，必要时编写自动化测试
- 测试失败时要有清晰的错误信息
- 保持测试与功能文档的同步
- **测试成功后必须删除测试文件**，保持项目目录整洁

## 版本控制

### Git规范
- 提交信息要清晰描述变更内容
- 小步提交，便于问题追踪
- 重要功能开发可使用分支
- 提交时同步更新相关文档

### 发布管理
- 使用简单的版本标识
- 重要变更要更新功能清单
- 尽量保持向后兼容性
- 数据结构变更要有说明文档

## 开发流程总结

### 新模块开发流程
1. **检查现有功能**：查看功能清单，确认无重复
2. **创建模块目录**：建立代码、文档结构
3. **编写功能文档**：描述模块功能和特性
4. **实现核心功能**：以简洁有效为目标
5. **编写API文档**：记录所有公开接口
6. **更新功能清单**：添加新实现的功能
7. **基本功能测试**：验证主要功能正确性

### 文档维护要求
- **开发过程中实时更新文档**
- **功能变更时同步修改功能清单**
- **API变更时立即更新API文档**
- **定期检查文档完整性和准确性**
